﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3A0889F4-9CE8-332E-933C-4A2A8424375E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22000.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\1127d3647aae09118b4504183023fa1b\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/foye_111111/build/aim_controller.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp;C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/foye_111111/build/aim_controller.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp;C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/foye_111111/build/aim_controller.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp;C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
D:\C++\bin\cmake.exe -SC:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller -BC:/Users/<USER>/Desktop/foye_111111/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/foye_111111/build/aim_controller.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeCXXCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeRCCompiler.cmake;C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\3.23.0\CMakeSystem.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\CMakeLists.txt;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\JoinPaths.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11Common.cmake;C:\Users\<USER>\Desktop\foye_地基版本（非常重要）\cpp_controller\pybind11\tools\pybind11NewTools.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig-version.cmake;D:\3rd_party\opencv_4.8\opencv\build\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;D:\3rd_party\opencv_4.8\opencv\build\x64\vc16\lib\OpenCVModules.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCCompilerABI.c;D:\C++\share\cmake-3.23\Modules\CMakeCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeCXXCompilerABI.cpp;D:\C++\share\cmake-3.23\Modules\CMakeCXXInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCommonLanguageInclude.cmake;D:\C++\share\cmake-3.23\Modules\CMakeCompilerIdDetection.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDependentOption.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompileFeatures.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerABI.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineCompilerId.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeDetermineSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeFindBinUtils.cmake;D:\C++\share\cmake-3.23\Modules\CMakeGenericSystem.cmake;D:\C++\share\cmake-3.23\Modules\CMakeInitializeConfigs.cmake;D:\C++\share\cmake-3.23\Modules\CMakeLanguageInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakePackageConfigHelpers.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitIncludeInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseImplicitLinkInfo.cmake;D:\C++\share\cmake-3.23\Modules\CMakeParseLibraryArchitecture.cmake;D:\C++\share\cmake-3.23\Modules\CMakeRCCompiler.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeRCInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystem.cmake.in;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInformation.cmake;D:\C++\share\cmake-3.23\Modules\CMakeSystemSpecificInitialize.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCXXCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestCompilerCommon.cmake;D:\C++\share\cmake-3.23\Modules\CMakeTestRCCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\CheckCXXSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ADSP-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMCC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\ARMClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\AppleClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Borland-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Comeau-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Cray-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GHS-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IAR-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Intel-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\MSVC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVHPC-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PGI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\PathScale-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SCO-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TI-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\Watcom-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-C-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;D:\C++\share\cmake-3.23\Modules\CompilerId\VS-10.vcxproj.in;D:\C++\share\cmake-3.23\Modules\FindPackageHandleStandardArgs.cmake;D:\C++\share\cmake-3.23\Modules\FindPackageMessage.cmake;D:\C++\share\cmake-3.23\Modules\FindPython.cmake;D:\C++\share\cmake-3.23\Modules\FindPython\Support.cmake;D:\C++\share\cmake-3.23\Modules\GNUInstallDirs.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckCompilerFlag.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckFlagCommonConfig.cmake;D:\C++\share\cmake-3.23\Modules\Internal\CheckSourceCompiles.cmake;D:\C++\share\cmake-3.23\Modules\Internal\FeatureTesting.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-Determine-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-C.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC-CXX.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows-MSVC.cmake;D:\C++\share\cmake-3.23\Modules\Platform\Windows.cmake;D:\C++\share\cmake-3.23\Modules\Platform\WindowsPaths.cmake;D:\C++\share\cmake-3.23\Modules\WriteBasicConfigVersionFile.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Desktop\foye_111111\build\CMakeFiles\generate.stamp;C:\Users\<USER>\Desktop\foye_111111\build\pybind11\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>