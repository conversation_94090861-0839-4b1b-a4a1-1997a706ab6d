#include "../include/ONNXInference.h"

ONNXInference::ONNXInference(const std::string& modelPath)
    : m_env(ORT_LOGGING_LEVEL_WARNING, "AimAssist")
    , m_session(nullptr)
    , m_modelPath(modelPath)
    , m_inputWidth(320)
    , m_inputHeight(320)
    , m_inputChannels(3)
    , m_initialized(false)
{
    memset(&m_stats, 0, sizeof(m_stats));
}

ONNXInference::~ONNXInference() {
    if (m_session) {
        delete m_session;
        m_session = nullptr;
    }
}

ErrorCode ONNXInference::Initialize(const std::string& modelPath) {
    if (m_initialized) {
        return ErrorCode::Success;
    }

    if (!modelPath.empty()) {
        m_modelPath = modelPath;
    }

    if (LoadModel(m_modelPath) != ErrorCode::Success) {
        return ErrorCode::ModelLoadFailed;
    }

    GetModelInfo();
    
    m_initialized = true;
    LOG_INFO("ONNX Inference initialized successfully");
    LOG_INFO("Model: " << m_modelPath);
    LOG_INFO("Input size: " << m_inputWidth << "x" << m_inputHeight << "x" << m_inputChannels);
    
    return ErrorCode::Success;
}

std::vector<float> ONNXInference::Inference(const cv::Mat& inputImage) {
    if (!m_initialized || !m_session) {
        return {};
    }

    StartTimer();

    try {
        // 预处理图像
        auto inputTensor = PreprocessImage(inputImage);
        
        // 创建输入张量
        std::vector<int64_t> inputShape = {1, m_inputChannels, m_inputHeight, m_inputWidth};
        auto memoryInfo = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value inputOrtTensor = Ort::Value::CreateTensor<float>(
            memoryInfo, inputTensor.data(), inputTensor.size(), inputShape.data(), inputShape.size());

        // 执行推理
        auto outputTensors = m_session->Run(Ort::RunOptions{nullptr}, 
                                          m_inputNames.data(), &inputOrtTensor, 1,
                                          m_outputNames.data(), m_outputNames.size());

        // 获取输出数据
        float* outputData = outputTensors[0].GetTensorMutableData<float>();
        auto outputShape = outputTensors[0].GetTensorTypeAndShapeInfo().GetShape();
        
        size_t outputSize = 1;
        for (auto dim : outputShape) {
            outputSize *= dim;
        }

        std::vector<float> result(outputData, outputData + outputSize);
        
        double inferenceTime = EndTimer();
        UpdatePerformanceStats(inferenceTime);
        
        return result;
    }
    catch (const std::exception& e) {
        LOG_ERROR("ONNX inference failed: " << e.what());
        return {};
    }
}

ErrorCode ONNXInference::LoadModel(const std::string& modelPath) {
    try {
        // 配置会话选项
        m_sessionOptions.SetIntraOpNumThreads(1);
        m_sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_EXTENDED);

        // 使用CPU提供程序
        LOG_INFO("Using CPU execution provider");

        // 创建会话
        std::wstring wModelPath(modelPath.begin(), modelPath.end());
        m_session = new Ort::Session(m_env, wModelPath.c_str(), m_sessionOptions);
        
        LOG_INFO("ONNX model loaded successfully: " << modelPath);
        return ErrorCode::Success;
    }
    catch (const std::exception& e) {
        LOG_ERROR("Failed to load ONNX model: " << e.what());
        return ErrorCode::ModelLoadFailed;
    }
}

void ONNXInference::GetModelInfo() {
    if (!m_session) return;

    // 获取输入信息
    size_t numInputNodes = m_session->GetInputCount();
    for (size_t i = 0; i < numInputNodes; i++) {
        auto inputName = m_session->GetInputNameAllocated(i, m_allocator);
        m_inputNames.push_back(inputName.get());

        auto inputTypeInfo = m_session->GetInputTypeInfo(i);
        auto inputTensorInfo = inputTypeInfo.GetTensorTypeAndShapeInfo();
        m_inputShape = inputTensorInfo.GetShape();
    }

    // 获取输出信息
    size_t numOutputNodes = m_session->GetOutputCount();
    for (size_t i = 0; i < numOutputNodes; i++) {
        auto outputName = m_session->GetOutputNameAllocated(i, m_allocator);
        m_outputNames.push_back(outputName.get());

        auto outputTypeInfo = m_session->GetOutputTypeInfo(i);
        auto outputTensorInfo = outputTypeInfo.GetTensorTypeAndShapeInfo();
        m_outputShape = outputTensorInfo.GetShape();
    }

    // 更新输入尺寸
    if (m_inputShape.size() >= 4) {
        m_inputChannels = static_cast<int>(m_inputShape[1]);
        m_inputHeight = static_cast<int>(m_inputShape[2]);
        m_inputWidth = static_cast<int>(m_inputShape[3]);
    }
}

std::vector<float> ONNXInference::PreprocessImage(const cv::Mat& image) {
    cv::Mat processedImage;
    
    // 确保图像是正确的尺寸
    if (image.size() != cv::Size(m_inputWidth, m_inputHeight)) {
        cv::resize(image, processedImage, cv::Size(m_inputWidth, m_inputHeight));
    } else {
        processedImage = image;
    }

    // 转换为float并归一化到[0,1]
    processedImage.convertTo(processedImage, CV_32F, 1.0 / 255.0);

    // 转换为CHW格式 (从HWC到CHW)
    std::vector<cv::Mat> channels(3);
    cv::split(processedImage, channels);

    std::vector<float> result;
    result.reserve(m_inputChannels * m_inputHeight * m_inputWidth);

    for (int c = 0; c < m_inputChannels; ++c) {
        float* data = reinterpret_cast<float*>(channels[c].data);
        result.insert(result.end(), data, data + m_inputHeight * m_inputWidth);
    }

    return result;
}

void ONNXInference::StartTimer() const {
    m_startTime = Utils::GetCurrentTime();
}

double ONNXInference::EndTimer() const {
    return Utils::GetElapsedTime(m_startTime);
}

void ONNXInference::UpdatePerformanceStats(double inferenceTime) const {
    m_stats.inferenceTimeMs = inferenceTime;
    m_stats.frameCount++;
}
