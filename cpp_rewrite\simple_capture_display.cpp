#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <iostream>
#include <chrono>
#include <vector>

#define SAFE_RELEASE(p) do { if(p) { (p)->Release(); (p) = nullptr; } } while(0)

class SimpleCaptureDisplay {
private:
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    int m_screenWidth;
    int m_screenHeight;
    int m_roiX, m_roiY;
    int m_roiSize;
    bool m_initialized;

public:
    SimpleCaptureDisplay(int roiSize = 320) 
        : m_pDevice(nullptr)
        , m_pContext(nullptr)
        , m_pDeskDupl(nullptr)
        , m_pStagingTexture(nullptr)
        , m_roiSize(roiSize)
        , m_initialized(false)
    {
    }

    ~SimpleCaptureDisplay() {
        Release();
    }

    bool Initialize() {
        if (m_initialized) {
            return true;
        }

        // 获取屏幕尺寸
        m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
        m_screenHeight = GetSystemMetrics(SM_CYSCREEN);
        
        // 计算屏幕中央ROI
        m_roiX = (m_screenWidth - m_roiSize) / 2;
        m_roiY = (m_screenHeight - m_roiSize) / 2;
        
        std::cout << "Screen size: " << m_screenWidth << "x" << m_screenHeight << std::endl;
        std::cout << "ROI center: (" << m_roiX << ", " << m_roiY << ") size: " << m_roiSize << "x" << m_roiSize << std::endl;

        // 初始化DXGI
        if (!InitializeDXGI()) {
            std::cerr << "Failed to initialize DXGI" << std::endl;
            return false;
        }

        m_initialized = true;
        std::cout << "SimpleCaptureDisplay initialized successfully" << std::endl;
        return true;
    }

    cv::Mat CaptureROI() {
        if (!m_initialized) {
            return cv::Mat();
        }

        // 获取完整屏幕截图
        std::vector<BYTE> frameBuffer;
        int width, height;
        
        HRESULT hr = GetNextFrame(frameBuffer, width, height);
        if (FAILED(hr) || hr == S_FALSE) {
            return cv::Mat();
        }

        // 创建完整屏幕的Mat (BGRA格式)
        cv::Mat fullScreen(height, width, CV_8UC4, frameBuffer.data());
        
        // 提取ROI区域
        cv::Rect roi(m_roiX, m_roiY, m_roiSize, m_roiSize);
        
        // 确保ROI在屏幕范围内
        roi.x = std::max(0, std::min(roi.x, width - 1));
        roi.y = std::max(0, std::min(roi.y, height - 1));
        roi.width = std::min(roi.width, width - roi.x);
        roi.height = std::min(roi.height, height - roi.y);
        
        cv::Mat roiFrame = fullScreen(roi);
        
        // 转换为RGB格式
        cv::Mat rgbFrame;
        cv::cvtColor(roiFrame, rgbFrame, cv::COLOR_BGRA2RGB);
        
        return rgbFrame.clone();
    }

    void Release() {
        SAFE_RELEASE(m_pStagingTexture);
        SAFE_RELEASE(m_pDeskDupl);
        SAFE_RELEASE(m_pContext);
        SAFE_RELEASE(m_pDevice);
        
        m_initialized = false;
        std::cout << "SimpleCaptureDisplay resources released" << std::endl;
    }

private:
    bool InitializeDXGI() {
        HRESULT hr = D3D11CreateDevice(
            nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
            nullptr, 0, D3D11_SDK_VERSION,
            &m_pDevice, nullptr, &m_pContext
        );
        
        if (FAILED(hr)) {
            std::cerr << "Failed to create D3D11 device: 0x" << std::hex << hr << std::endl;
            return false;
        }
        
        // 获取DXGI链
        IDXGIDevice* pDxgiDevice = nullptr;
        hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
        if (FAILED(hr)) return false;
        
        IDXGIAdapter* pDxgiAdapter = nullptr;
        hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
        SAFE_RELEASE(pDxgiDevice);
        if (FAILED(hr)) return false;
        
        IDXGIOutput* pDxgiOutput = nullptr;
        hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput);
        SAFE_RELEASE(pDxgiAdapter);
        if (FAILED(hr)) return false;
        
        IDXGIOutput1* pDxgiOutput1 = nullptr;
        hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
        SAFE_RELEASE(pDxgiOutput);
        if (FAILED(hr)) return false;
        
        hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
        SAFE_RELEASE(pDxgiOutput1);
        if (FAILED(hr)) return false;
        
        return true;
    }

    bool CreateStagingTexture() {
        if (m_pStagingTexture) {
            return true;
        }
        
        DXGI_OUTDUPL_DESC duplDesc;
        m_pDeskDupl->GetDesc(&duplDesc);
        
        D3D11_TEXTURE2D_DESC desc = {};
        desc.Width = duplDesc.ModeDesc.Width;
        desc.Height = duplDesc.ModeDesc.Height;
        desc.MipLevels = 1;
        desc.ArraySize = 1;
        desc.Format = duplDesc.ModeDesc.Format;
        desc.SampleDesc.Count = 1;
        desc.SampleDesc.Quality = 0;
        desc.Usage = D3D11_USAGE_STAGING;
        desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
        desc.BindFlags = 0;
        desc.MiscFlags = 0;
        
        HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
        return SUCCEEDED(hr);
    }

    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
        if (!m_pDeskDupl) {
            return E_FAIL;
        }

        HRESULT hr;
        IDXGIResource* pDesktopResource = nullptr;
        DXGI_OUTDUPL_FRAME_INFO frameInfo;

        // 获取下一帧
        hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);
        
        if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
            return S_FALSE; // 没有新帧
        }
        if (FAILED(hr)) {
            return hr;
        }

        // 转换为纹理
        ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
        hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
        SAFE_RELEASE(pDesktopResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 创建中转纹理
        if (!CreateStagingTexture()) {
            SAFE_RELEASE(pAcquiredDesktopImage);
            m_pDeskDupl->ReleaseFrame();
            return E_FAIL;
        }

        // 复制到中转纹理
        m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
        SAFE_RELEASE(pAcquiredDesktopImage);

        // 映射纹理数据
        D3D11_MAPPED_SUBRESOURCE mappedResource;
        hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
        if (FAILED(hr)) {
            m_pDeskDupl->ReleaseFrame();
            return hr;
        }

        // 高效内存拷贝
        const UINT rowPitch = mappedResource.RowPitch;
        const UINT framePitch = m_screenWidth * 4; // BGRA
        frameBuffer.resize(framePitch * m_screenHeight);
        
        BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
        BYTE* pDest = frameBuffer.data();
        
        if (rowPitch == framePitch) {
            // 一次性拷贝
            memcpy(pDest, pSource, framePitch * m_screenHeight);
        } else {
            // 逐行拷贝
            for (int y = 0; y < m_screenHeight; ++y) {
                memcpy(pDest, pSource, framePitch);
                pSource += rowPitch;
                pDest += framePitch;
            }
        }

        // 清理
        m_pContext->Unmap(m_pStagingTexture, 0);
        m_pDeskDupl->ReleaseFrame();

        width = m_screenWidth;
        height = m_screenHeight;

        return S_OK;
    }
};

int main() {
    std::cout << "=== Simple Capture Display (320x320 Center ROI) ===" << std::endl;

    SimpleCaptureDisplay capture(320);
    
    if (!capture.Initialize()) {
        std::cerr << "Failed to initialize capture" << std::endl;
        return -1;
    }

    // 创建显示窗口
    cv::namedWindow("Screen Center 320x320", cv::WINDOW_NORMAL);
    cv::resizeWindow("Screen Center 320x320", 640, 640); // 放大2倍显示

    std::cout << "Capturing screen center 320x320 region..." << std::endl;
    std::cout << "Press 'q' to quit, 's' to save screenshot, 'f' to toggle fullscreen" << std::endl;

    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    auto lastFpsTime = startTime;
    bool fullscreen = false;

    while (true) {
        cv::Mat frame = capture.CaptureROI();
        
        if (frame.empty()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        frameCount++;

        // 转换为BGR用于显示
        cv::Mat displayFrame;
        cv::cvtColor(frame, displayFrame, cv::COLOR_RGB2BGR);
        
        // 放大显示
        cv::Mat enlargedFrame;
        cv::resize(displayFrame, enlargedFrame, cv::Size(640, 640), 0, 0, cv::INTER_NEAREST);

        // 显示FPS和信息
        auto currentTime = std::chrono::high_resolution_clock::now();
        if (std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastFpsTime).count() >= 1000) {
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            double fps = frameCount * 1000.0 / duration.count();
            
            std::cout << "Frame " << frameCount << " | FPS: " << fps << std::endl;
            lastFpsTime = currentTime;
        }

        // 在图像上显示信息
        std::string fpsText = "Frame: " + std::to_string(frameCount);
        std::string sizeText = "ROI: 320x320 (Center)";
        std::string controlText = "Q:Quit S:Save F:Fullscreen";
        
        cv::putText(enlargedFrame, fpsText, cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 255, 0), 2);
        cv::putText(enlargedFrame, sizeText, cv::Point(10, 70), cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 255, 0), 2);
        cv::putText(enlargedFrame, controlText, cv::Point(10, 110), cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 0), 2);

        // 绘制中心十字线
        int centerX = enlargedFrame.cols / 2;
        int centerY = enlargedFrame.rows / 2;
        cv::line(enlargedFrame, cv::Point(centerX - 20, centerY), cv::Point(centerX + 20, centerY), cv::Scalar(0, 0, 255), 2);
        cv::line(enlargedFrame, cv::Point(centerX, centerY - 20), cv::Point(centerX, centerY + 20), cv::Scalar(0, 0, 255), 2);

        cv::imshow("Screen Center 320x320", enlargedFrame);

        // 处理按键
        int key = cv::waitKey(1) & 0xFF;
        if (key == 'q' || key == 27) { // 'q' 或 ESC
            break;
        } else if (key == 's') { // 保存截图
            std::string filename = "center_capture_" + std::to_string(frameCount) + ".jpg";
            cv::imwrite(filename, displayFrame);
            std::cout << "Screenshot saved: " << filename << std::endl;
        } else if (key == 'f') { // 切换全屏
            fullscreen = !fullscreen;
            if (fullscreen) {
                cv::setWindowProperty("Screen Center 320x320", cv::WND_PROP_FULLSCREEN, cv::WINDOW_FULLSCREEN);
            } else {
                cv::setWindowProperty("Screen Center 320x320", cv::WND_PROP_FULLSCREEN, cv::WINDOW_NORMAL);
                cv::resizeWindow("Screen Center 320x320", 640, 640);
            }
        }
    }

    // 最终统计
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    double avgFps = frameCount * 1000.0 / totalDuration.count();

    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total frames: " << frameCount << std::endl;
    std::cout << "Runtime: " << totalDuration.count() << " ms" << std::endl;
    std::cout << "Average FPS: " << avgFps << std::endl;

    cv::destroyAllWindows();
    std::cout << "Program completed!" << std::endl;

    return 0;
}
