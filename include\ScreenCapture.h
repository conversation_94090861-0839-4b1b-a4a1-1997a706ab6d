#pragma once

#include <windows.h>
#include <d3d11.h>
#include <dxgi1_2.h>
#include <opencv2/opencv.hpp>
#include <vector>
#include <memory>

// 安全释放宏
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }

/**
 * 高性能屏幕截图类
 * 集成DXGI截图和OpenCV图像预处理
 * 专为AI模型输入优化
 */
class ScreenCapture {
public:
    /**
     * 构造函数
     * @param roiX ROI区域左上角X坐标 (-1表示全屏)
     * @param roiY ROI区域左上角Y坐标 (-1表示全屏)
     * @param roiWidth ROI区域宽度 (-1表示全屏)
     * @param roiHeight ROI区域高度 (-1表示全屏)
     * @param targetSize 输出图像尺寸 (默认320x320)
     */
    ScreenCapture(int roiX = -1, int roiY = -1, int roiWidth = -1, int roiHeight = -1, int targetSize = 320);
    
    /**
     * 析构函数
     */
    ~ScreenCapture();

    /**
     * 初始化截图器
     * @return 成功返回true，失败返回false
     */
    bool Initialize();

    /**
     * 截图并预处理
     * @return 预处理后的RGB图像(320x320)，失败返回空Mat
     */
    cv::Mat CaptureAndPreprocess();

    /**
     * 获取原始截图(BGRA格式)
     * @return 原始BGRA图像，失败返回空Mat
     */
    cv::Mat CaptureRaw();

    /**
     * 释放资源
     */
    void Release();

    /**
     * 检查是否已初始化
     */
    bool IsInitialized() const { return m_initialized; }

    /**
     * 获取截图区域信息
     */
    void GetROI(int& x, int& y, int& width, int& height) const {
        x = m_roiX; y = m_roiY; width = m_roiWidth; height = m_roiHeight;
    }

    /**
     * 获取性能统计
     */
    struct PerformanceStats {
        double captureTimeMs;      // 截图耗时
        double preprocessTimeMs;   // 预处理耗时
        double totalTimeMs;        // 总耗时
        int frameCount;            // 帧计数
        double avgFps;             // 平均FPS
    };
    
    PerformanceStats GetPerformanceStats() const { return m_stats; }

private:
    // DXGI相关成员
    ID3D11Device* m_pDevice;
    ID3D11DeviceContext* m_pContext;
    IDXGIOutputDuplication* m_pDeskDupl;
    ID3D11Texture2D* m_pStagingTexture;
    
    // 配置参数
    int m_screenWidth, m_screenHeight;
    int m_roiX, m_roiY, m_roiWidth, m_roiHeight;
    int m_targetSize;
    bool m_useROI;
    bool m_initialized;
    
    // 图像缓冲区
    std::vector<BYTE> m_frameBuffer;
    cv::Mat m_bgraBuffer;      // DXGI输出的BGRA缓冲区
    cv::Mat m_rgbBuffer;       // 转换后的RGB缓冲区
    cv::Mat m_resizedBuffer;   // 调整尺寸后的缓冲区
    
    // 性能统计
    mutable PerformanceStats m_stats;
    mutable LARGE_INTEGER m_frequency;
    mutable LARGE_INTEGER m_startTime;
    
    // 私有方法
    bool InitializeDXGI();
    bool CreateStagingTexture();
    HRESULT GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height);
    void PreallocateBuffers();
    void UpdatePerformanceStats(double captureTime, double preprocessTime) const;
    void StartTimer() const;
    double EndTimer() const;
};

/**
 * 截图器工厂类
 * 用于创建和管理截图器实例
 */
class ScreenCaptureFactory {
public:
    /**
     * 创建屏幕截图器
     * @param config 配置参数
     * @return 截图器智能指针
     */
    static std::unique_ptr<ScreenCapture> CreateScreenCapture(
        int roiX = -1, int roiY = -1, int roiWidth = -1, int roiHeight = -1, int targetSize = 320);
    
    /**
     * 检查系统是否支持DXGI截图
     */
    static bool IsDXGISupported();
};
