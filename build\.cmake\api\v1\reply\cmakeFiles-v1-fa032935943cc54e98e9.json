{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/<PERSON>-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/GNU-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/HP-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/LCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/XL-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/zOS-C-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CompilerId/VS-10.vcxproj.in"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCCompilerABI.c"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Platform/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDetermineCompileFeatures.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "C:/Users/<USER>/Desktop/foye_111111/build/CMakeFiles/3.23.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/OpenCVConfig-version.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/OpenCVConfig.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules-debug.cmake"}, {"isExternal": true, "path": "D:/3rd_party/opencv_4.8/opencv/build/x64/vc16/lib/OpenCVModules-release.cmake"}, {"path": "pybind11/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeDependentOption.cmake"}, {"path": "pybind11/tools/pybind11Common.cmake"}, {"path": "pybind11/tools/pybind11NewTools.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPython/Support.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CheckCXXCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CheckCXXSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckCompilerFlag.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckFlagCommonConfig.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/Internal/CheckSourceCompiles.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++/share/cmake-3.23/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake"}, {"path": "pybind11/tools/JoinPaths.cmake"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/Desktop/foye_111111/build", "source": "C:/Users/<USER>/Desktop/foye_地基版本（非常重要）/cpp_controller"}, "version": {"major": 1, "minor": 0}}