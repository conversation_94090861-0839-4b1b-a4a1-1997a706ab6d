#include "../include/ScreenCapture.h"
#include <iostream>
#include <chrono>

ScreenCapture::ScreenCapture(int roiX, int roiY, int roiWidth, int roiHeight, int targetSize)
    : m_pDevice(nullptr)
    , m_pContext(nullptr)
    , m_pDeskDupl(nullptr)
    , m_pStagingTexture(nullptr)
    , m_roiX(roiX)
    , m_roiY(roiY)
    , m_roiWidth(roiWidth)
    , m_roiHeight(roiHeight)
    , m_targetSize(targetSize)
    , m_useROI(roiX >= 0 && roiY >= 0 && roiWidth > 0 && roiHeight > 0)
    , m_initialized(false)
    , m_screenWidth(0)
    , m_screenHeight(0)
{
    // 初始化性能计数器
    QueryPerformanceFrequency(&m_frequency);
    memset(&m_stats, 0, sizeof(m_stats));
}

ScreenCapture::~ScreenCapture() {
    Release();
}

bool ScreenCapture::Initialize() {
    if (m_initialized) {
        return true;
    }

    // 获取屏幕尺寸
    m_screenWidth = GetSystemMetrics(SM_CXSCREEN);
    m_screenHeight = GetSystemMetrics(SM_CYSCREEN);

    // 如果没有指定ROI，使用屏幕中心区域
    if (!m_useROI) {
        int defaultSize = 640; // 默认截图区域大小
        m_roiX = (m_screenWidth - defaultSize) / 2;
        m_roiY = (m_screenHeight - defaultSize) / 2;
        m_roiWidth = defaultSize;
        m_roiHeight = defaultSize;
        m_useROI = true;
    }

    // 初始化DXGI
    if (!InitializeDXGI()) {
        std::cerr << "Failed to initialize DXGI" << std::endl;
        return false;
    }

    // 预分配缓冲区
    PreallocateBuffers();

    m_initialized = true;
    std::cout << "ScreenCapture initialized successfully" << std::endl;
    std::cout << "ROI: (" << m_roiX << ", " << m_roiY << ", " << m_roiWidth << ", " << m_roiHeight << ")" << std::endl;
    std::cout << "Target size: " << m_targetSize << "x" << m_targetSize << std::endl;

    return true;
}

cv::Mat ScreenCapture::CaptureAndPreprocess() {
    if (!m_initialized) {
        return cv::Mat();
    }

    StartTimer();

    // 1. DXGI截图
    std::vector<BYTE> frameBuffer;
    int width, height;
    HRESULT hr = GetNextFrame(frameBuffer, width, height);

    double captureTime = EndTimer();

    if (FAILED(hr)) {
        return cv::Mat();
    }

    if (hr == S_FALSE) {
        // 没有新帧，返回空Mat
        return cv::Mat();
    }

    StartTimer();

    // 2. 创建OpenCV Mat (BGRA格式)
    cv::Mat bgraFrame(height, width, CV_8UC4, frameBuffer.data());

    // 3. 提取ROI区域
    cv::Rect roi(m_roiX, m_roiY, m_roiWidth, m_roiHeight);
    
    // 确保ROI在图像范围内
    roi.x = std::max(0, std::min(roi.x, width - 1));
    roi.y = std::max(0, std::min(roi.y, height - 1));
    roi.width = std::min(roi.width, width - roi.x);
    roi.height = std::min(roi.height, height - roi.y);
    
    cv::Mat roiFrame = bgraFrame(roi);

    // 4. BGRA → RGB转换
    cv::cvtColor(roiFrame, m_rgbBuffer, cv::COLOR_BGRA2RGB);

    // 5. 尺寸调整到目标尺寸
    cv::resize(m_rgbBuffer, m_resizedBuffer, cv::Size(m_targetSize, m_targetSize), 0, 0, cv::INTER_LINEAR);

    double preprocessTime = EndTimer();

    // 更新性能统计
    UpdatePerformanceStats(captureTime, preprocessTime);

    return m_resizedBuffer.clone();
}

cv::Mat ScreenCapture::CaptureRaw() {
    if (!m_initialized) {
        return cv::Mat();
    }

    std::vector<BYTE> frameBuffer;
    int width, height;
    HRESULT hr = GetNextFrame(frameBuffer, width, height);

    if (FAILED(hr) || hr == S_FALSE) {
        return cv::Mat();
    }

    // 返回原始BGRA图像
    return cv::Mat(height, width, CV_8UC4, frameBuffer.data()).clone();
}

void ScreenCapture::Release() {
    SAFE_RELEASE(m_pStagingTexture);
    SAFE_RELEASE(m_pDeskDupl);
    SAFE_RELEASE(m_pContext);
    SAFE_RELEASE(m_pDevice);
    
    m_initialized = false;
    std::cout << "ScreenCapture resources released" << std::endl;
}

bool ScreenCapture::InitializeDXGI() {
    HRESULT hr = S_OK;

    // 1. 创建D3D11设备
    D3D_FEATURE_LEVEL featureLevel;
    hr = D3D11CreateDevice(
        nullptr,                    // 使用默认适配器
        D3D_DRIVER_TYPE_HARDWARE,   // 硬件加速
        nullptr,                    // 软件光栅化器
        0,                          // 创建标志
        nullptr,                    // 特性级别数组
        0,                          // 特性级别数组大小
        D3D11_SDK_VERSION,          // SDK版本
        &m_pDevice,                 // 输出设备
        &featureLevel,              // 输出特性级别
        &m_pContext                 // 输出设备上下文
    );

    if (FAILED(hr)) {
        std::cerr << "Failed to create D3D11 device: " << std::hex << hr << std::endl;
        return false;
    }

    // 2. 获取DXGI设备
    IDXGIDevice* pDxgiDevice = nullptr;
    hr = m_pDevice->QueryInterface(__uuidof(IDXGIDevice), reinterpret_cast<void**>(&pDxgiDevice));
    if (FAILED(hr)) {
        std::cerr << "Failed to get DXGI device: " << std::hex << hr << std::endl;
        return false;
    }

    // 3. 获取DXGI适配器
    IDXGIAdapter* pDxgiAdapter = nullptr;
    hr = pDxgiDevice->GetParent(__uuidof(IDXGIAdapter), reinterpret_cast<void**>(&pDxgiAdapter));
    SAFE_RELEASE(pDxgiDevice);
    if (FAILED(hr)) {
        std::cerr << "Failed to get DXGI adapter: " << std::hex << hr << std::endl;
        return false;
    }

    // 4. 获取输出
    IDXGIOutput* pDxgiOutput = nullptr;
    hr = pDxgiAdapter->EnumOutputs(0, &pDxgiOutput); // 主显示器
    SAFE_RELEASE(pDxgiAdapter);
    if (FAILED(hr)) {
        std::cerr << "Failed to enumerate outputs: " << std::hex << hr << std::endl;
        return false;
    }

    // 5. 获取输出1接口
    IDXGIOutput1* pDxgiOutput1 = nullptr;
    hr = pDxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), reinterpret_cast<void**>(&pDxgiOutput1));
    SAFE_RELEASE(pDxgiOutput);
    if (FAILED(hr)) {
        std::cerr << "Failed to get IDXGIOutput1: " << std::hex << hr << std::endl;
        return false;
    }

    // 6. 创建桌面复制
    hr = pDxgiOutput1->DuplicateOutput(m_pDevice, &m_pDeskDupl);
    SAFE_RELEASE(pDxgiOutput1);
    if (FAILED(hr)) {
        std::cerr << "Failed to duplicate output: " << std::hex << hr << std::endl;
        return false;
    }

    return true;
}

void ScreenCapture::PreallocateBuffers() {
    // 预分配帧缓冲区
    int bufferSize = m_screenWidth * m_screenHeight * 4; // BGRA
    m_frameBuffer.reserve(bufferSize);

    // 预分配OpenCV Mat
    m_bgraBuffer = cv::Mat::zeros(m_roiHeight, m_roiWidth, CV_8UC4);
    m_rgbBuffer = cv::Mat::zeros(m_roiHeight, m_roiWidth, CV_8UC3);
    m_resizedBuffer = cv::Mat::zeros(m_targetSize, m_targetSize, CV_8UC3);
}

void ScreenCapture::StartTimer() const {
    QueryPerformanceCounter(&m_startTime);
}

double ScreenCapture::EndTimer() const {
    LARGE_INTEGER endTime;
    QueryPerformanceCounter(&endTime);
    return static_cast<double>(endTime.QuadPart - m_startTime.QuadPart) * 1000.0 / m_frequency.QuadPart;
}

void ScreenCapture::UpdatePerformanceStats(double captureTime, double preprocessTime) const {
    m_stats.captureTimeMs = captureTime;
    m_stats.preprocessTimeMs = preprocessTime;
    m_stats.totalTimeMs = captureTime + preprocessTime;
    m_stats.frameCount++;

    // 计算平均FPS (每100帧更新一次)
    if (m_stats.frameCount % 100 == 0) {
        m_stats.avgFps = 1000.0 / m_stats.totalTimeMs;
    }
}

HRESULT ScreenCapture::GetNextFrame(std::vector<BYTE>& frameBuffer, int& width, int& height) {
    if (!m_pDeskDupl) {
        return E_FAIL; // 未初始化
    }

    HRESULT hr;
    IDXGIResource* pDesktopResource = nullptr;
    DXGI_OUTDUPL_FRAME_INFO frameInfo;

    // 1. 获取下一帧，超时设为 0 (立即返回)
    hr = m_pDeskDupl->AcquireNextFrame(0, &frameInfo, &pDesktopResource);

    // 如果是因为超时（没有新帧），则返回 S_FALSE
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        return S_FALSE;
    }
    // 如果是其他失败，则返回错误代码
    if (FAILED(hr)) {
        return hr;
    }

    // 2. 将获取到的资源转换为ID3D11Texture2D
    ID3D11Texture2D* pAcquiredDesktopImage = nullptr;
    hr = pDesktopResource->QueryInterface(__uuidof(ID3D11Texture2D), reinterpret_cast<void**>(&pAcquiredDesktopImage));
    SAFE_RELEASE(pDesktopResource);
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return hr;
    }

    // 3. 如果中转纹理还未创建，则创建它
    if (!m_pStagingTexture) {
        if (!CreateStagingTexture()) {
            SAFE_RELEASE(pAcquiredDesktopImage);
            m_pDeskDupl->ReleaseFrame();
            return E_FAIL;
        }
    }

    // 4. 将GPU上的纹理复制到CPU可访问的中转纹理
    m_pContext->CopyResource(m_pStagingTexture, pAcquiredDesktopImage);
    SAFE_RELEASE(pAcquiredDesktopImage);

    // 5. 映射中转纹理以读取数据
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    hr = m_pContext->Map(m_pStagingTexture, 0, D3D11_MAP_READ, 0, &mappedResource);
    if (FAILED(hr)) {
        m_pDeskDupl->ReleaseFrame();
        return hr;
    }

    // 6. 将纹理数据复制到我们自己的缓冲区
    const UINT rowPitch = mappedResource.RowPitch;
    const UINT framePitch = m_screenWidth * 4; // BGRA格式，每个像素4字节
    frameBuffer.resize(framePitch * m_screenHeight);

    BYTE* pSource = static_cast<BYTE*>(mappedResource.pData);
    BYTE* pDest = frameBuffer.data();
    for (int y = 0; y < m_screenHeight; ++y) {
        memcpy(pDest, pSource, framePitch);
        pSource += rowPitch; // 移动到下一行（源）
        pDest += framePitch; // 移动到下一行（目标）
    }

    // 7. 解除映射并释放帧
    m_pContext->Unmap(m_pStagingTexture, 0);
    m_pDeskDupl->ReleaseFrame();

    // 传出图像尺寸
    width = m_screenWidth;
    height = m_screenHeight;

    return S_OK;
}

bool ScreenCapture::CreateStagingTexture() {
    // 获取桌面复制的描述
    DXGI_OUTDUPL_DESC duplDesc;
    m_pDeskDupl->GetDesc(&duplDesc);

    // 创建中转纹理描述
    D3D11_TEXTURE2D_DESC desc = {};
    desc.Width = duplDesc.ModeDesc.Width;
    desc.Height = duplDesc.ModeDesc.Height;
    desc.MipLevels = 1;
    desc.ArraySize = 1;
    desc.Format = duplDesc.ModeDesc.Format;
    desc.SampleDesc.Count = 1;
    desc.SampleDesc.Quality = 0;
    desc.Usage = D3D11_USAGE_STAGING;
    desc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    desc.BindFlags = 0;
    desc.MiscFlags = 0;

    HRESULT hr = m_pDevice->CreateTexture2D(&desc, nullptr, &m_pStagingTexture);
    if (FAILED(hr)) {
        std::cerr << "Failed to create staging texture: " << std::hex << hr << std::endl;
        return false;
    }

    return true;
}

// ScreenCaptureFactory 实现
std::unique_ptr<ScreenCapture> ScreenCaptureFactory::CreateScreenCapture(
    int roiX, int roiY, int roiWidth, int roiHeight, int targetSize) {

    auto capture = std::make_unique<ScreenCapture>(roiX, roiY, roiWidth, roiHeight, targetSize);

    if (!capture->Initialize()) {
        std::cerr << "Failed to initialize ScreenCapture" << std::endl;
        return nullptr;
    }

    return capture;
}

bool ScreenCaptureFactory::IsDXGISupported() {
    // 尝试创建一个临时的D3D11设备来检查DXGI支持
    ID3D11Device* pDevice = nullptr;
    ID3D11DeviceContext* pContext = nullptr;

    HRESULT hr = D3D11CreateDevice(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        nullptr,
        0,
        D3D11_SDK_VERSION,
        &pDevice,
        nullptr,
        &pContext
    );

    bool supported = SUCCEEDED(hr);

    SAFE_RELEASE(pContext);
    SAFE_RELEASE(pDevice);

    return supported;
}
