@echo off
echo === C++ 截图模块构建脚本 ===

:: 设置变量
set BUILD_DIR=build_capture
set CMAKE_GENERATOR="Visual Studio 16 2019"
set CMAKE_ARCH=x64
set BUILD_TYPE=Release

:: 检查CMake是否存在
cmake --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到CMake，请确保CMake已安装并添加到PATH
    pause
    exit /b 1
)

:: 检查OpenCV路径
if not exist "D:\3rd_party\opencv_4.8\opencv\build" (
    echo 错误: 未找到OpenCV，请确保路径正确: D:\3rd_party\opencv_4.8\opencv\build
    pause
    exit /b 1
)

:: 创建构建目录
if not exist %BUILD_DIR% (
    mkdir %BUILD_DIR%
)

cd %BUILD_DIR%

:: 配置项目
echo 正在配置CMake项目...
cmake .. -G %CMAKE_GENERATOR% -A %CMAKE_ARCH% -DCMAKE_BUILD_TYPE=%BUILD_TYPE%
if errorlevel 1 (
    echo 错误: CMake配置失败
    cd ..
    pause
    exit /b 1
)

:: 构建项目
echo 正在构建项目...
cmake --build . --config %BUILD_TYPE% --parallel
if errorlevel 1 (
    echo 错误: 构建失败
    cd ..
    pause
    exit /b 1
)

cd ..

echo.
echo === 构建完成 ===
echo 可执行文件位置: %BUILD_DIR%\bin\%BUILD_TYPE%\test_capture.exe
echo 静态库位置: %BUILD_DIR%\lib\%BUILD_TYPE%\AimAssist.lib
echo.
echo 运行测试程序:
echo cd %BUILD_DIR%\bin\%BUILD_TYPE%
echo test_capture.exe
echo.

:: 询问是否立即运行测试
set /p choice="是否立即运行测试程序? (y/n): "
if /i "%choice%"=="y" (
    cd %BUILD_DIR%\bin\%BUILD_TYPE%
    if exist test_capture.exe (
        echo 启动测试程序...
        test_capture.exe
    ) else (
        echo 错误: 未找到测试程序
    )
    cd ..\..\..
)

pause
