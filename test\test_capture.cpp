#include "../include/ScreenCapture.h"
#include <iostream>
#include <chrono>
#include <thread>

int main() {
    std::cout << "=== C++ 截图模块测试程序 ===" << std::endl;
    
    // 检查DXGI支持
    if (!ScreenCaptureFactory::IsDXGISupported()) {
        std::cerr << "错误: 系统不支持DXGI截图" << std::endl;
        return -1;
    }
    std::cout << "✓ DXGI支持检查通过" << std::endl;

    // 获取屏幕尺寸
    int screenWidth = GetSystemMetrics(SM_CXSCREEN);
    int screenHeight = GetSystemMetrics(SM_CYSCREEN);
    std::cout << "屏幕尺寸: " << screenWidth << "x" << screenHeight << std::endl;

    // 计算中心区域ROI (640x640)
    int roiSize = 640;
    int roiX = (screenWidth - roiSize) / 2;
    int roiY = (screenHeight - roiSize) / 2;
    
    std::cout << "ROI区域: (" << roiX << ", " << roiY << ", " << roiSize << ", " << roiSize << ")" << std::endl;

    // 创建截图器
    auto capture = ScreenCaptureFactory::CreateScreenCapture(roiX, roiY, roiSize, roiSize, 320);
    if (!capture) {
        std::cerr << "错误: 创建截图器失败" << std::endl;
        return -1;
    }
    std::cout << "✓ 截图器创建成功" << std::endl;

    // 创建显示窗口
    cv::namedWindow("原始截图 (BGRA)", cv::WINDOW_NORMAL);
    cv::namedWindow("预处理后 (RGB 320x320)", cv::WINDOW_NORMAL);
    cv::resizeWindow("原始截图 (BGRA)", 640, 640);
    cv::resizeWindow("预处理后 (RGB 320x320)", 320, 320);

    std::cout << "\n开始截图测试..." << std::endl;
    std::cout << "按 'q' 键退出, 按 's' 键保存当前帧" << std::endl;

    int frameCount = 0;
    auto startTime = std::chrono::high_resolution_clock::now();
    
    while (true) {
        // 截图并预处理
        cv::Mat processedFrame = capture->CaptureAndPreprocess();
        
        if (processedFrame.empty()) {
            // 没有新帧，稍微等待
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            continue;
        }

        frameCount++;

        // 获取原始截图用于显示
        cv::Mat rawFrame = capture->CaptureRaw();
        if (!rawFrame.empty()) {
            // 提取ROI区域显示
            cv::Rect roi(roiX, roiY, roiSize, roiSize);
            roi.x = std::max(0, std::min(roi.x, rawFrame.cols - 1));
            roi.y = std::max(0, std::min(roi.y, rawFrame.rows - 1));
            roi.width = std::min(roi.width, rawFrame.cols - roi.x);
            roi.height = std::min(roi.height, rawFrame.rows - roi.y);
            
            cv::Mat roiFrame = rawFrame(roi);
            cv::Mat bgrFrame;
            cv::cvtColor(roiFrame, bgrFrame, cv::COLOR_BGRA2BGR);
            cv::imshow("原始截图 (BGRA)", bgrFrame);
        }

        // 显示预处理后的图像
        cv::Mat displayFrame;
        cv::cvtColor(processedFrame, displayFrame, cv::COLOR_RGB2BGR);
        cv::imshow("预处理后 (RGB 320x320)", displayFrame);

        // 每100帧显示一次性能统计
        if (frameCount % 100 == 0) {
            auto currentTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime);
            double fps = frameCount * 1000.0 / duration.count();
            
            auto stats = capture->GetPerformanceStats();
            std::cout << "\n=== 性能统计 (第" << frameCount << "帧) ===" << std::endl;
            std::cout << "平均FPS: " << fps << std::endl;
            std::cout << "截图耗时: " << stats.captureTimeMs << " ms" << std::endl;
            std::cout << "预处理耗时: " << stats.preprocessTimeMs << " ms" << std::endl;
            std::cout << "总耗时: " << stats.totalTimeMs << " ms" << std::endl;
        }

        // 处理按键
        int key = cv::waitKey(1) & 0xFF;
        if (key == 'q' || key == 27) { // 'q' 或 ESC
            break;
        } else if (key == 's') { // 保存当前帧
            std::string filename = "capture_frame_" + std::to_string(frameCount) + ".jpg";
            cv::imwrite(filename, displayFrame);
            std::cout << "已保存: " << filename << std::endl;
        }
    }

    // 最终统计
    auto endTime = std::chrono::high_resolution_clock::now();
    auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    double avgFps = frameCount * 1000.0 / totalDuration.count();

    std::cout << "\n=== 最终统计 ===" << std::endl;
    std::cout << "总帧数: " << frameCount << std::endl;
    std::cout << "运行时间: " << totalDuration.count() << " ms" << std::endl;
    std::cout << "平均FPS: " << avgFps << std::endl;

    cv::destroyAllWindows();
    std::cout << "测试完成!" << std::endl;

    return 0;
}
