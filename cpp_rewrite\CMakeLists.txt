cmake_minimum_required(VERSION 3.16)
project(AimAssistWithInference VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译选项
if(MSVC)
    add_compile_options(/W4 /permissive-)
    # 优化选项
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /Ob2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    # 设置编码为UTF-8
    add_compile_options(/utf-8)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")
endif()

# 查找OpenCV
set(OpenCV_DIR "D:/3rd_party/opencv_4.8/opencv/build")
find_package(OpenCV REQUIRED)

if(OpenCV_FOUND)
    message(STATUS "OpenCV found: ${OpenCV_VERSION}")
    message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
else()
    message(FATAL_ERROR "OpenCV not found!")
endif()

# 查找ONNX Runtime DirectML
set(ONNXRUNTIME_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty/Microsoft.ML.OnnxRuntime.DirectML.1.22.1")
find_path(ONNXRUNTIME_INCLUDE_DIR
    NAMES onnxruntime_cxx_api.h
    PATHS ${ONNXRUNTIME_ROOT_PATH}/build/native/include
)

find_library(ONNXRUNTIME_LIB
    NAMES onnxruntime
    PATHS ${ONNXRUNTIME_ROOT_PATH}/runtimes/win-x64/native
)

if(ONNXRUNTIME_INCLUDE_DIR AND ONNXRUNTIME_LIB)
    message(STATUS "ONNX Runtime found")
    message(STATUS "ONNX Runtime include: ${ONNXRUNTIME_INCLUDE_DIR}")
    message(STATUS "ONNX Runtime library: ${ONNXRUNTIME_LIB}")
else()
    message(FATAL_ERROR "ONNX Runtime not found!")
endif()

# 查找DirectML
set(DIRECTML_ROOT_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty/Microsoft.AI.DirectML.1.15.4")
find_path(DIRECTML_INCLUDE_DIR
    NAMES DirectML.h
    PATHS ${DIRECTML_ROOT_PATH}/include
)

find_library(DIRECTML_LIB
    NAMES DirectML
    PATHS ${DIRECTML_ROOT_PATH}/bin/x64-win
)

if(DIRECTML_INCLUDE_DIR AND DIRECTML_LIB)
    message(STATUS "DirectML found")
    message(STATUS "DirectML include: ${DIRECTML_INCLUDE_DIR}")
    message(STATUS "DirectML library: ${DIRECTML_LIB}")
else()
    message(WARNING "DirectML not found, using CPU only")
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${OpenCV_INCLUDE_DIRS}
    ${ONNXRUNTIME_INCLUDE_DIR}
    ${DIRECTML_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/../3rdparty
)

# 源文件
set(SOURCES
    capture_with_inference.cpp
    src/ONNXInference.cpp
)

# 创建可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})

# 链接库
target_link_libraries(${PROJECT_NAME}
    ${OpenCV_LIBS}
    ${ONNXRUNTIME_LIB}
    ${DIRECTML_LIB}
    d3d11.lib
    dxgi.lib
    user32.lib
    gdi32.lib
    kernel32.lib
)

# 设置输出目录
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
)

# 复制ONNX Runtime和DirectML DLL
if(WIN32)
    # 复制ONNX Runtime DLL
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ONNXRUNTIME_ROOT_PATH}/runtimes/win-x64/native/onnxruntime.dll"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )

    # 复制ONNX Runtime Providers DLL
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${ONNXRUNTIME_ROOT_PATH}/runtimes/win-x64/native/onnxruntime_providers_shared.dll"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )

    # 复制DirectML DLL
    add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
        "${DIRECTML_ROOT_PATH}/bin/x64-win/DirectML.dll"
        $<TARGET_FILE_DIR:${PROJECT_NAME}>
    )
endif()

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
