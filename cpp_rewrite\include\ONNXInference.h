#pragma once

#include "Common.h"
#include <onnxruntime_cxx_api.h>

/**
 * ONNX模型推理类
 * 负责加载模型和执行推理
 */
class ONNXInference {
public:
    /**
     * 构造函数
     * @param modelPath 模型文件路径
     */
    ONNXInference(const std::string& modelPath = "");
    
    /**
     * 析构函数
     */
    ~ONNXInference();

    /**
     * 初始化推理引擎
     * @param modelPath 模型文件路径
     * @return 成功返回Success，失败返回相应错误码
     */
    ErrorCode Initialize(const std::string& modelPath);

    /**
     * 执行推理
     * @param inputImage 输入图像 (RGB格式，320x320)
     * @return 推理结果张量，失败返回空vector
     */
    std::vector<float> Inference(const cv::Mat& inputImage);

    /**
     * 获取输入尺寸
     */
    std::pair<int, int> GetInputSize() const { return {m_inputWidth, m_inputHeight}; }

    /**
     * 获取输出形状
     */
    std::vector<int64_t> GetOutputShape() const { return m_outputShape; }

    /**
     * 检查是否已初始化
     */
    bool IsInitialized() const { return m_initialized; }

    /**
     * 获取性能统计
     */
    PerformanceStats GetPerformanceStats() const { return m_stats; }

private:
    // ONNX Runtime相关
    Ort::Env m_env;
    Ort::Session* m_session;
    Ort::SessionOptions m_sessionOptions;
    Ort::AllocatorWithDefaultOptions m_allocator;
    
    // 模型信息
    std::string m_modelPath;
    std::vector<const char*> m_inputNames;
    std::vector<const char*> m_outputNames;
    std::vector<int64_t> m_inputShape;
    std::vector<int64_t> m_outputShape;
    
    // 输入输出尺寸
    int m_inputWidth;
    int m_inputHeight;
    int m_inputChannels;
    
    // 状态
    bool m_initialized;
    
    // 性能统计
    mutable PerformanceStats m_stats;
    mutable TimePoint m_startTime;
    
    // 私有方法
    ErrorCode LoadModel(const std::string& modelPath);
    void GetModelInfo();
    std::vector<float> PreprocessImage(const cv::Mat& image);
    void StartTimer() const;
    double EndTimer() const;
    void UpdatePerformanceStats(double inferenceTime) const;
};
